using UnityEngine;

namespace AdapterTool.Runtime
{
    public class AdapterBase<TComponent, TConfig> : MonoBehaviour, IAdapter
        where TComponent : Component
        where TConfig : struct
    {
        [SerializeField] public TConfig portraitLayout;
        [SerializeField] public TConfig landscapeLayout;
        private TComponent _mComponent;

        protected TComponent mComponent
        {
            get
            {
                if (_mComponent == null) _mComponent = GetComponent<TComponent>();
                return _mComponent;
            }
        }

        protected void Awake()
        {
            Debug.Log("Awake: " + gameObject.name);
            // 在Awake中注册到管理器，与OnDestroy对应
            AdapterManager.Instance?.RegisterAdapter(this);
        }

        protected void OnDestroy()
        {
            // 从管理器中取消注册
            AdapterManager.Instance?.UnregisterAdapter(this);
        }

        public virtual void Apply(ScreenOrientationType orientation)
        {
            Debug.Log("Apply: " + gameObject.name + " Orientation: " + orientation);
            if (orientation == ScreenOrientationType.Landscape)
                ApplyConfig(landscapeLayout);
            else if (orientation == ScreenOrientationType.Portrait)
                ApplyConfig(portraitLayout);
        }
        
        #if UNITY_EDITOR
        public virtual void Save()
        {
            Debug.Log("Save: " + gameObject.name);
            // if 
            // var config = new TConfig();
            // SaveConfig(ref config);
            // var orientation = AdapterManager.Instance?.CurrentOrientation ?? ScreenOrientationType.Portrait;
            // if (orientation == ScreenOrientationType.Landscape)
            //     landscapeLayout = config;
            // else if (orientation == ScreenOrientationType.Portrait)
            //     portraitLayout = config;
        }
        #endif


        protected virtual void ApplyConfig(in TConfig config)
        {
        }

#if UNITY_EDITOR
        protected virtual void SaveConfig(ref TConfig config)
        {
        }
#endif
    }
}