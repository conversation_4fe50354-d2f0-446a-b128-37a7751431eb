using UnityEngine;

namespace AdapterTool.Runtime
{
    public class AdapterBase<TComponent, TConfig> : MonoBehaviour
        where TComponent : Component
        where TConfig : struct
    {
        [SerializeField] public TConfig portraitLayout;

        [SerializeField] public TConfig landscapeLayout;
        private TComponent _mComponent;

        protected TComponent mComponent
        {
            get
            {
                if (_mComponent == null) _mComponent = GetComponent<TComponent>();

                return _mComponent;
            }
        }

        protected void Awake()
        {
            Debug.Log("Awake: " + gameObject.name);
            if (AdapterManager.Instance != null) AdapterManager.OnOrientationChanged += Apply;
        }

        protected void OnDestroy()
        {
            if (AdapterManager.Instance != null) AdapterManager.OnOrientationChanged -= Apply;
        }

        public virtual void Apply(ScreenOrientationType orientation)
        {
            Debug.Log("Apply: " + gameObject.name + " Orientation: " + orientation);
            if (orientation == ScreenOrientationType.Landscape)
                ApplyConfig(landscapeLayout);
            else if (orientation == ScreenOrientationType.Portrait)
                ApplyConfig(portraitLayout);
        }


        protected virtual void ApplyConfig(in TConfig config)
        {
        }

#if UNITY_EDITOR
        protected virtual void SaveConfig(ref TConfig config)
        {
        }
#endif
    }
}