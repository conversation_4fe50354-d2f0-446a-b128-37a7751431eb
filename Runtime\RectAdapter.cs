﻿using System;
using UnityEngine;

namespace AdapterTool.Runtime
{
    [Serializable]
    public struct RectTransformAdaptConfig
    {
        public Quaternion mLocalRotation;
        public Vector3 mLocalPosition;
        public Vector3 mLocalScale;
        public Vector2 mAnchorMin;
        public Vector2 mAnchorMax;
        public Vector2 mAnchoredPosition;
        public Vector2 mSizeDelta;
        public Vector2 mPivot;
    }

    public class RectAdapter : AdapterBase<RectTransform, RectTransformAdaptConfig>
    {
        protected override void ApplyConfig(in RectTransformAdaptConfig config)
        {
            base.ApplyConfig(config);

            if (mComponent.localRotation != config.mLocalRotation) mComponent.localRotation = config.mLocalRotation;
            if (mComponent.localPosition != config.mLocalPosition) mComponent.localPosition = config.mLocalPosition;
            if (mComponent.localScale != config.mLocalScale) mComponent.localScale = config.mLocalScale;
            if (mComponent.anchorMin != config.mAnchorMin) mComponent.anchorMin = config.mAnchorMin;
            if (mComponent.anchorMax != config.mAnchorMax) mComponent.anchorMax = config.mAnchorMax;
            if (mComponent.anchoredPosition != config.mAnchoredPosition)
                mComponent.anchoredPosition = config.mAnchoredPosition;
            if (mComponent.sizeDelta != config.mSizeDelta) mComponent.sizeDelta = config.mSizeDelta;
            if (mComponent.pivot != config.mPivot) mComponent.pivot = config.mPivot;
        }


        protected override void SaveConfig(ref RectTransformAdaptConfig config)
        {
            base.SaveConfig(ref config);
            config.mLocalRotation = mComponent.localRotation;
            config.mLocalPosition = mComponent.localPosition;
            config.mLocalScale = mComponent.localScale;
            config.mAnchorMin = mComponent.anchorMin;
            config.mAnchorMax = mComponent.anchorMax;
            config.mAnchoredPosition = mComponent.anchoredPosition;
            config.mSizeDelta = mComponent.sizeDelta;
            config.mPivot = mComponent.pivot;
        }
    }
}