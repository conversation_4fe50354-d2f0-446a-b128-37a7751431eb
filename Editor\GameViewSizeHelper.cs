﻿using UnityEditor;
using UnityEditorInternal;
using System;
using System.Reflection;
using UnityEngine;

namespace AdapterTool.Editor
{
    public static class GameViewSizeHelper
    {
        private static object gameViewSizesInstance;
        private static MethodInfo getGroup;
        private static PropertyInfo currentGroupTypeProp;

        static GameViewSizeHelper()
        {
            var sizesType = typeof(UnityEditor.Editor).Assembly.GetType("UnityEditor.GameViewSizes");
            var singleType = typeof(ScriptableSingleton<>).MakeGenericType(sizesType);
            var instanceProp = singleType.GetProperty("instance");
            gameViewSizesInstance = instanceProp.GetValue(null);
            getGroup = sizesType.GetMethod("GetGroup");
            currentGroupTypeProp = sizesType.GetProperty("currentGroupType");
        }

        private static object GetGroup(int groupType) =>
            getGroup.Invoke(gameViewSizesInstance, new object[] { (object)groupType });

        private static int GetCurrentGroupType()
        {
            var groupTypeEnum = currentGroupTypeProp.GetValue(gameViewSizesInstance);
            return (int)groupTypeEnum;
        }

        private static Vector2 GameViewSize()
        {
            try
            {
                var asm = typeof(EditorWindow).Assembly;

                var playModeViewType = asm.GetType("UnityEditor.PlayModeView");
                var mi = playModeViewType?.GetMethod(
                    "GetMainPlayModeViewTargetSize",
                    BindingFlags.NonPublic | BindingFlags.Static);

                if (mi != null)
                {
                    var obj = mi.Invoke(null, null); // static -> target 传 null
                    if (obj is Vector2 v) return v;
                }
            }
            catch
            {
                /* 避免在编辑器里抛异常中断 */
                Debug.LogError("获取失败");
                return Vector2.zero;
            }

            return Vector2.zero;
        }

        private static Vector2 GetCurrentGameViewSize()
        {
            Vector2 gameViewSize = GameViewSize();
            return gameViewSize;
        }

        public static bool IsLandscape()
        {
            Vector2 size = GetCurrentGameViewSize();
            return size.x > size.y;
        }

        public static bool IsPortrait()
        {
            Vector2 size = GetCurrentGameViewSize();
            return size.x < size.y;
        }

        public static void AddCustomResolution(int width, int height, string label)
        {
            int groupType = GetCurrentGroupType();
            var group = GetGroup(groupType);
            var getDisplayTexts = group.GetType().GetMethod("GetDisplayTexts");
            var displayTexts = (string[])getDisplayTexts.Invoke(group, null);

            foreach (var text in displayTexts)
            {
                if (text.StartsWith(label, StringComparison.OrdinalIgnoreCase))
                {
                    return; // Already exists
                }
            }

            var addCustomSize = group.GetType().GetMethod("AddCustomSize");
            var gvsType = typeof(UnityEditor.Editor).Assembly.GetType("UnityEditor.GameViewSize");
            var gvsTypeEnum = typeof(UnityEditor.Editor).Assembly.GetType("UnityEditor.GameViewSizeType");
            var ctor = gvsType.GetConstructor(new Type[] { gvsTypeEnum, typeof(int), typeof(int), typeof(string) });
            var newSize = ctor.Invoke(new object[]
                { Enum.Parse(gvsTypeEnum, "FixedResolution"), width, height, label });
            addCustomSize.Invoke(group, new object[] { newSize });
            Debug.Log($"成功请求为当前平台添加分辨率: {label} ({width}x{height})");
        }

        private static void SwitchToResolutionByLabel(string label)
        {
            Debug.Log("switch to resolution: " + label);
            int groupType = GetCurrentGroupType();
            var group = GetGroup(groupType);
            var getDisplayTexts = group.GetType().GetMethod("GetDisplayTexts");
            var displayTexts = (string[])getDisplayTexts.Invoke(group, null);

            int targetIndex = -1;
            for (int i = 0; i < displayTexts.Length; i++)
            {
                if (displayTexts[i].StartsWith(label, StringComparison.OrdinalIgnoreCase))
                {
                    targetIndex = i;
                    break;
                }
            }

            if (targetIndex == -1)
            {
                Debug.LogError($"在当前平台列表中未找到标签为 '{label}' 的分辨率");
                return;
            }

            var assembly = typeof(UnityEditor.Editor).Assembly;
            var gvWndType = assembly.GetType("UnityEditor.GameView");
            var window = EditorWindow.GetWindow(gvWndType);
            if (window == null) return;

            var sizeSelectionCallbackMethod = gvWndType.GetMethod("SizeSelectionCallback",
                BindingFlags.Instance | BindingFlags.Public | BindingFlags.NonPublic);
            if (sizeSelectionCallbackMethod != null)
            {
                sizeSelectionCallbackMethod.Invoke(window, new object[] { targetIndex, null });
            }
        }

        public static void AddAndSwitchToResolution(int width, int height, string label)
        {
            AddCustomResolution(width, height, label);
            EditorApplication.delayCall += () => { SwitchToResolutionByLabel(label); };
        }

        public static void SetGameViewLandscape()
        {
            var resolution = WulinUiToolManager.Instance.LandscapeResolution;
            int width = (int)resolution.x;
            int height = (int)resolution.y;
            AddAndSwitchToResolution(width, height, $"Landscape {width}x{height}");
        }

        public static void SetGameViewPortrait()
        {
            var resolution = WulinUiToolManager.Instance.PortraitResolution;
            int width = (int)resolution.x;
            int height = (int)resolution.y;
            AddAndSwitchToResolution(width, height, $"Portrait {width}x{height}");
        }
    }
}