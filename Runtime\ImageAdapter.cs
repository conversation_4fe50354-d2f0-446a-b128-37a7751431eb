﻿using System;
using UnityEngine;
using UnityEngine.UI;

namespace AdapterTool.Runtime
{
    [Serializable]
    public struct ImageAdaptConfig
    {
        public Sprite mSprite;
        public Color mColor;
        public Material mMaterial;
        public bool mRaycastTarget;
        public bool mMaskable;
        public bool mPreserveAspect;
        public Image.Type mType;
        public float mFillAmount;
        public Image.FillMethod mFillMethod;
        public bool mFillClockwise;
        public int mFillOrigin;

        public bool mUseSpriteMesh;
    }

    public class ImageAdapter : AdapterBase<Image, ImageAdaptConfig>
    {
        protected override void ApplyConfig(in ImageAdaptConfig config)
        {
            base.ApplyConfig(config);

            if (mComponent.sprite != config.mSprite) mComponent.sprite = config.mSprite;
            if (mComponent.color != config.mColor) mComponent.color = config.mColor;
            if (mComponent.material != config.mMaterial) mComponent.material = config.mMaterial;
            if (mComponent.raycastTarget != config.mRaycastTarget) mComponent.raycastTarget = config.mRaycastTarget;
            if (mComponent.maskable != config.mMaskable) mComponent.maskable = config.mMaskable;
            if (mComponent.preserveAspect != config.mPreserveAspect) mComponent.preserveAspect = config.mPreserveAspect;
            if (mComponent.type != config.mType) mComponent.type = config.mType;
            if (Math.Abs(mComponent.fillAmount - config.mFillAmount) > 0.001f)
                mComponent.fillAmount = config.mFillAmount;
            if (mComponent.fillMethod != config.mFillMethod) mComponent.fillMethod = config.mFillMethod;
            if (mComponent.fillClockwise != config.mFillClockwise) mComponent.fillClockwise = config.mFillClockwise;
            if (mComponent.fillOrigin != config.mFillOrigin) mComponent.fillOrigin = config.mFillOrigin;
            if (mComponent.useSpriteMesh != config.mUseSpriteMesh) mComponent.useSpriteMesh = config.mUseSpriteMesh;
        }

        protected override void SaveConfig(ref ImageAdaptConfig config)
        {
            base.SaveConfig(ref config);
            if (mComponent == null) return;
            config.mSprite = mComponent.sprite;
            config.mColor = mComponent.color;
            config.mMaterial = mComponent.material;
            config.mRaycastTarget = mComponent.raycastTarget;
            config.mMaskable = mComponent.maskable;
            config.mPreserveAspect = mComponent.preserveAspect;
            config.mType = mComponent.type;
            config.mFillAmount = mComponent.fillAmount;
            config.mFillMethod = mComponent.fillMethod;
            config.mFillClockwise = mComponent.fillClockwise;
            config.mFillOrigin = mComponent.fillOrigin;
            config.mUseSpriteMesh = mComponent.useSpriteMesh;
        }
    }
}