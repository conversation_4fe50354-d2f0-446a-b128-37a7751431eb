using AdapterTool.Runtime;
using UnityEditor;
using UnityEditor.SceneManagement;
using UnityEngine;

namespace AdapterTool.Editor
{
    /// <summary>
    /// 这个是 prefab编辑模式下的界面
    /// </summary>
    [InitializeOnLoad]
    public static class AdapterToolEditor
    {
        private static bool isPortrait = true; // 默认竖屏

        static AdapterToolEditor()
        {
            SceneView.duringSceneGui += OnSceneGUI;
        }

        private static void OnSceneGUI(SceneView sceneView)
        {
            Handles.BeginGUI();

            isPortrait = GameViewSizeHelper.IsPortrait();

            GUILayout.BeginArea(new Rect(sceneView.position.width - 150, 10, 140, 100), EditorStyles.helpBox);

            var modeText = isPortrait ? "当前：竖屏模式" : "当前：横屏模式";
            GUILayout.Label(modeText, EditorStyles.boldLabel);

            if (GUILayout.Button(isPortrait ? "切换为横屏" : "切换为竖屏"))
            {
                isPortrait = !isPortrait;

                ApplyLayoutChange(); // 调用自定义逻辑切换布局
            }

            if (GUILayout.Button("给所有组件加上扩展组件"))
            {
                var prefabStage = PrefabStageUtility.GetCurrentPrefabStage();
                if (prefabStage != null)
                {
                    var prefabRoot = prefabStage.prefabContentsRoot;
                    if (prefabRoot != null)
                    {
                        AdapterToolUtils.AddExtComponents(prefabRoot);
                        Debug.Log($"已为 Prefab：{prefabRoot.name} 添加扩展组件");
                    }
                }
                else
                {
                    Debug.LogWarning("当前不在 Prefab 编辑模式中，未处理任何对象。");
                }
            }

            GUILayout.EndArea();

            Handles.EndGUI();
        }

        private static void ApplyLayoutChange()
        {
            if (isPortrait)
                GameViewSizeHelper.SetGameViewPortrait();
            else
                GameViewSizeHelper.SetGameViewLandscape();
            EditorApplication.delayCall += () => { EditorWindow.FocusWindowIfItsOpen<SceneView>(); };

            // 在编辑器模式下也可以触发适配器更新（如果有AdapterManager实例）
            if (AdapterManager.Instance != null)
            {
                var targetOrientation = isPortrait ? ScreenOrientationType.Portrait : ScreenOrientationType.Landscape;
                AdapterManager.Instance.SetOrientation(targetOrientation);
            }
        }
    }
}