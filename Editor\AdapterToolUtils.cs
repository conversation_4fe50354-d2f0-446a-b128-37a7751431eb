using AdapterTool.Runtime;
using UnityEngine;

namespace AdapterTool.Editor
{
    public class AdapterToolUtils
    {
        public static void AddExtComponents(GameObject prefabRoot)
        {
            TraverseChildren(prefabRoot.transform);
        }

        private static void TraverseChildren(Transform current)
        {
            Debug.Log($"Object Name: {current.name}");
            AddExtComponent(current);
            foreach (Transform child in current)
            {
                // 递归调用
                TraverseChildren(child);
            }
        }

        private static void AddIfMissing<TBase, TExt>(Transform current)
            where TBase : Component
            where TExt : Component
        {
            if (current.GetComponent<TBase>())
            {
                if (!current.GetComponent<TExt>())
                {
                    current.gameObject.AddComponent<TExt>();
                }
            }
        }

        private static void AddExtComponent(Transform current)
        {
            // todo 可以试着用 attributes来做。后续观察一下
            AddIfMissing<RectTransform, RectAdapter>(current);
            // AddIfMissing<UnityEngine.UI.Image, ImageExt>(current);
        }

        public static void SaveValues()
        {
        }
    }
}