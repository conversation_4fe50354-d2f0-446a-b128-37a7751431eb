# 适配器工具架构改造说明

## 改造概述

将原有的**事件订阅模式**改为**管理器直接引用调用模式**，提高性能和可控性。

## 主要变更

### 1. AdapterManager 变更
- **移除**: `public static event Action<ScreenOrientationType> OnOrientationChanged;`
- **新增**: `IAdapter` 接口定义
- **新增**: `List<IAdapter> m_RegisteredAdapters` 保存所有适配器引用
- **新增**: `RegisterAdapter()` 和 `UnregisterAdapter()` 方法
- **修改**: `ToggleOrientation()` 和 `SetOrientation()` 直接调用适配器而非触发事件

### 2. AdapterBase 变更
- **实现**: `IAdapter` 接口
- **修改**: 在 `Awake()` 中注册到管理器，在 `OnDestroy()` 中取消注册
- **移除**: 事件订阅和取消订阅代码

### 3. 新增文件
- `Runtime/AdapterTest.cs`: 测试脚本，提供GUI和键盘测试功能

## 为什么选择 Awake() 和 OnDestroy()？

**最终选择 `Awake()` 和 `OnDestroy()` 的原因：**

1. **生命周期对应**: `Awake()` 和 `OnDestroy()` 是完美对应的Unity生命周期方法
2. **初始化安全**: `AdapterManager` 的单例在自己的 `Awake()` 中初始化，其他组件在 `Awake()` 中注册是安全的
3. **尽早注册**: 在 `Awake()` 中注册确保适配器尽早可用
4. **避免不匹配**: 避免了 `Start()` 和 `OnDestroy()` 不对应的问题

**备选方案考虑**：
- `Start()` + `OnDisable()`: 适用于需要动态启用/禁用的场景
- `OnEnable()` + `OnDisable()`: 适用于频繁启用/禁用的组件

## 架构优势

### 原事件模式的问题：
- 事件调用有性能开销
- 难以控制调用顺序
- 内存泄漏风险（忘记取消订阅）
- 调试困难

### 新引用模式的优势：
- **性能更好**: 直接方法调用，无事件开销
- **更可控**: 管理器可以控制调用顺序和条件
- **更安全**: 自动清理空引用，避免内存泄漏
- **易调试**: 可以直接查看注册的适配器列表
- **扩展性强**: 可以轻松添加批量操作、条件过滤等功能

## 使用方法

### 1. 基本使用（无变化）
```csharp
// 继承 AdapterBase 即可自动注册
public class MyAdapter : AdapterBase<Image, MyConfig>
{
    protected override void ApplyConfig(in MyConfig config)
    {
        // 应用配置
    }
}
```

### 2. 手动控制
```csharp
// 切换朝向
AdapterManager.Instance.ToggleOrientation();

// 设置特定朝向
AdapterManager.Instance.SetOrientation(ScreenOrientationType.Portrait);

// 获取当前朝向
var current = AdapterManager.Instance.GetCurrentOrientation();
```

### 3. 测试功能
- 添加 `AdapterTest` 组件到场景中
- 使用键盘快捷键：
  - `Space`: 切换朝向
  - `P`: 设置为竖屏
  - `L`: 设置为横屏
- 或使用屏幕上的GUI按钮

## 兼容性

- **向后兼容**: 现有的适配器组件无需修改，会自动使用新架构
- **编辑器支持**: 编辑器工具已更新，支持新的调用方式
- **性能提升**: 在大量适配器的场景中性能会有明显提升

## 注意事项

1. 确保场景中有 `AdapterManager` 实例
2. 适配器会在 `Start()` 时自动注册，在 `OnDestroy()` 时自动取消注册
3. 管理器会自动清理无效引用，无需担心内存泄漏
4. 可以通过 `AdapterTest` 脚本监控注册的适配器数量
