using System;
using System.Collections.Generic;
using UnityEngine;

namespace AdapterTool.Runtime
{
    public enum ScreenOrientationType
    {
        Landscape,
        Portrait
    }

    public interface IAdapter
    {
        void Apply(ScreenOrientationType orientation);
    }

    public class AdapterManager : MonoBehaviour
    {
        public static AdapterManager Instance;

        [Tooltip("设置游戏启动时的初始朝向")] [SerializeField]
        private readonly ScreenOrientationType m_StartOrientation = ScreenOrientationType.Portrait;

        private bool currentIsLandscape;
        private ScreenOrientationType m_CurrentOrientation;

        // 保存所有适配器的引用
        private List<IAdapter> m_RegisteredAdapters = new List<IAdapter>();

        private void Awake()
        {
            Debug.Log("awake");

            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
            }
            else
            {
                Destroy(gameObject);
            }

            currentIsLandscape = IsLandscape();
            m_CurrentOrientation = m_StartOrientation;
        }

        private void Start()
        {
            // 游戏启动时，根据预设的初始值，调用所有已注册的适配器
            ApplyOrientationToAllAdapters(m_CurrentOrientation);
        }

        // 注册适配器
        public void RegisterAdapter(IAdapter adapter)
        {
            if (adapter != null && !m_RegisteredAdapters.Contains(adapter))
            {
                m_RegisteredAdapters.Add(adapter);
                Debug.Log($"Registered adapter: {adapter}");

                // 立即应用当前朝向
                adapter.Apply(m_CurrentOrientation);
            }
        }

        // 取消注册适配器
        public void UnregisterAdapter(IAdapter adapter)
        {
            if (adapter != null && m_RegisteredAdapters.Contains(adapter))
            {
                m_RegisteredAdapters.Remove(adapter);
                Debug.Log($"Unregistered adapter: {adapter}");
            }
        }

        // 应用朝向到所有适配器
        private void ApplyOrientationToAllAdapters(ScreenOrientationType orientation)
        {
            Debug.Log($"Applying orientation {orientation} to {m_RegisteredAdapters.Count} adapters");

            for (int i = m_RegisteredAdapters.Count - 1; i >= 0; i--)
            {
                var adapter = m_RegisteredAdapters[i];
                if (adapter != null)
                {
                    adapter.Apply(orientation);
                }
                else
                {
                    // 清理空引用
                    m_RegisteredAdapters.RemoveAt(i);
                }
            }
        }


        // 核心：提供给按钮调用的公共方法
        public void ToggleOrientation()
        {
            // 切换状态
            m_CurrentOrientation = m_CurrentOrientation == ScreenOrientationType.Portrait
                ? ScreenOrientationType.Landscape
                : ScreenOrientationType.Portrait;

            Debug.Log($"Button clicked. Switching to: {m_CurrentOrientation}");

            // 直接调用所有已注册的适配器
            ApplyOrientationToAllAdapters(m_CurrentOrientation);
        }

        // (可选) 提供一个更明确的设置方法
        public void SetOrientation(ScreenOrientationType newOrientation)
        {
            if (m_CurrentOrientation == newOrientation) return;

            m_CurrentOrientation = newOrientation;
            Debug.Log($"Setting orientation to: {m_CurrentOrientation}");

            // 直接调用所有已注册的适配器
            ApplyOrientationToAllAdapters(m_CurrentOrientation);
        }


        public ScreenOrientationType GetCurrentOrientation()

        {
            return m_CurrentOrientation;
        }


        public static bool IsLandscape()

        {
            return Screen.height < Screen.width;
        }


        public static bool IsPortrait()

        {
            return Screen.height >= Screen.width;
        }
    }
}