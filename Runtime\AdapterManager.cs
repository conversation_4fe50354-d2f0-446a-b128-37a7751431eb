using System;
using UnityEngine;

namespace AdapterTool.Runtime
{
    public enum ScreenOrientationType
    {
        Landscape,
        Portrait
    }

    public class AdapterManager : MonoBehaviour

    {
        public static AdapterManager Instance;

        [Tooltip("设置游戏启动时的初始朝向")] [SerializeField]
        private ScreenOrientationType m_StartOrientation = ScreenOrientationType.Portrait;


        private bool currentIsLandscape;

        private ScreenOrientationType m_CurrentOrientation;


        private void Awake()

        {
            Debug.Log("awake");

            if (Instance == null)

            {
                Instance = this;

                DontDestroyOnLoad(gameObject);
            }

            else

            {
                Destroy(gameObject);
            }


            currentIsLandscape = IsLandscape();

            m_CurrentOrientation = m_StartOrientation;
        }


        private void Start()

        {
// 游戏启动时，根据预设的初始值，广播一次事件，让所有UI就位
            OnOrientationChanged?.Invoke(m_CurrentOrientation);
        }


        public static event Action<ScreenOrientationType> OnOrientationChanged;


//  核心：提供给按钮调用的公共方法

        public void ToggleOrientation()

        {
// 切换状态

            m_CurrentOrientation = m_CurrentOrientation == ScreenOrientationType.Portrait
                ? ScreenOrientationType.Landscape
                : ScreenOrientationType.Portrait;


            Debug.Log($"Button clicked. Switching to: {m_CurrentOrientation}");


// 广播事件，通知所有监听者更新UI

            OnOrientationChanged?.Invoke(m_CurrentOrientation);
        }
        
        

// (可选) 提供一个更明确的设置方法

        public void SetOrientation(ScreenOrientationType newOrientation)

        {
            if (m_CurrentOrientation == newOrientation) return;


            m_CurrentOrientation = newOrientation;

            Debug.Log($"Setting orientation to: {m_CurrentOrientation}");

            OnOrientationChanged?.Invoke(m_CurrentOrientation);
        }


        public ScreenOrientationType GetCurrentOrientation()

        {
            return m_CurrentOrientation;
        }


        public static bool IsLandscape()

        {
            return Screen.height < Screen.width;
        }


        public static bool IsPortrait()

        {
            return Screen.height >= Screen.width;
        }
    }
}