﻿// using UnityEngine;
// using UnityEditor;
// using WulinUITools.Runtime;
//
// [CustomEditor(typeof(RectExt))]
// public class RectExtEditor : Editor
// {
//     public override void OnInspectorGUI()
//     {
//         DrawDefaultInspector();
//         
//         var targetType = target.GetType();
//         RectExt rectExt = (RectExt)target;
//         var rt = rectExt.Base;
//
//         EditorGUILayout.Space();
//         EditorGUILayout.LabelField("布局操作", EditorStyles.boldLabel);
//
//         if (GUILayout.Button("保存当前为 Portrait"))
//         {
//             Undo.RecordObject(rectExt, "Save Portrait Layout");
//             rectExt.portraitLayout = RectExt.RectTransformSnapshot.From(rt);
//             EditorUtility.SetDirty(rectExt);
//         }
//
//         if (GUILayout.Button("保存当前为 Landscape"))
//         {
//             Undo.RecordObject(rectExt, "Save Landscape Layout");
//             rectExt.landscapeLayout = RectExt.RectTransformSnapshot.From(rt);
//             EditorUtility.SetDirty(rectExt);
//         }
//
//         if (GUILayout.Button("应用 Portrait"))
//         {
//             Undo.RecordObject(rt, "Apply Portrait Layout");
//             rectExt.portraitLayout.ApplyConfig(rt);
//             EditorUtility.SetDirty(rt);
//         }
//
//         if (GUILayout.Button("应用 Landscape"))
//         {
//             Undo.RecordObject(rt, "Apply Landscape Layout");
//             rectExt.landscapeLayout.ApplyConfig(rt);
//             EditorUtility.SetDirty(rt);
//         }
//     }
// }
//
// // using UnityEngine;
// // using UnityEditor;
// // using System;
// // using System.Reflection;
// // using WulinUITools.Runtime;
// //
// // [CustomEditor(typeof(ExtBase<RectTransform, RectExt.RectTransformSnapshot>), true)]
// // public class ExtBaseEditor : Editor
// // {
// //     public override bool RequiresConstantRepaint() => false;
// //
// //     public override void OnInspectorGUI()
// //     {
// //         DrawDefaultInspector();
// //
// //         // 只处理继承自 ExtBase<> 的对象
// //         var targetType = target.GetType();
// //         if (!IsSubclassOfRawGeneric(typeof(ExtBase<>), targetType))
// //             return;
// //
// //         var baseComponentProp = targetType.GetProperty("Base", BindingFlags.Public | BindingFlags.Instance);
// //         if (baseComponentProp == null) return;
// //
// //         var baseComponent = baseComponentProp.GetValue(target);
// //
// //         var portraitField = targetType.GetField("portraitLayout");
// //         var landscapeField = targetType.GetField("landscapeLayout");
// //
// //         if (portraitField == null || landscapeField == null) return;
// //
// //         var snapshotType = portraitField.FieldType;
// //
// //         var fromMethod = snapshotType.GetMethod("From", BindingFlags.Public | BindingFlags.Static);
// //         var applyToMethod = snapshotType.GetMethod("ApplyTo", BindingFlags.Public | BindingFlags.Instance);
// //
// //         if (fromMethod == null || applyToMethod == null)
// //         {
// //             EditorGUILayout.HelpBox("Snapshot 类型缺少 From() 或 ApplyTo()", MessageType.Warning);
// //             return;
// //         }
// //
// //         EditorGUILayout.Space();
// //         EditorGUILayout.LabelField("布局操作", EditorStyles.boldLabel);
// //
// //         if (GUILayout.Button("保存当前为 Portrait"))
// //         {
// //             Undo.RecordObject(target, "Save Portrait Layout");
// //             var snap = fromMethod.Invoke(null, new[] { baseComponent });
// //             portraitField.SetValue(target, snap);
// //             EditorUtility.SetDirty(target);
// //         }
// //
// //         if (GUILayout.Button("保存当前为 Landscape"))
// //         {
// //             Undo.RecordObject(target, "Save Landscape Layout");
// //             var snap = fromMethod.Invoke(null, new[] { baseComponent });
// //             landscapeField.SetValue(target, snap);
// //             EditorUtility.SetDirty(target);
// //         }
// //
// //         if (GUILayout.Button("应用 Portrait"))
// //         {
// //             var snap = portraitField.GetValue(target);
// //             Undo.RecordObject(baseComponent as UnityEngine.Object, "Apply Portrait Layout");
// //             applyToMethod.Invoke(snap, new[] { baseComponent });
// //             EditorUtility.SetDirty(baseComponent as UnityEngine.Object);
// //         }
// //
// //         if (GUILayout.Button("应用 Landscape"))
// //         {
// //             var snap = landscapeField.GetValue(target);
// //             Undo.RecordObject(baseComponent as UnityEngine.Object, "Apply Landscape Layout");
// //             applyToMethod.Invoke(snap, new[] { baseComponent });
// //             EditorUtility.SetDirty(baseComponent as UnityEngine.Object);
// //         }
// //     }
// //
// //     private static bool IsSubclassOfRawGeneric(Type generic, Type toCheck)
// //     {
// //         while (toCheck != null && toCheck != typeof(MonoBehaviour))
// //         {
// //             var cur = toCheck.IsGenericType ? toCheck.GetGenericTypeDefinition() : toCheck;
// //             if (generic == cur)
// //                 return true;
// //             toCheck = toCheck.BaseType;
// //         }
// //         return false;
// //     }
// // }
//

