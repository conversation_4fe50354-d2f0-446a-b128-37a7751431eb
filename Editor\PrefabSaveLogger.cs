﻿using UnityEditor;
using UnityEngine;
using System.IO;
using UnityEditor.SceneManagement;
using UnityEngine.SceneManagement;

[InitializeOnLoad]
public static class PrefabSaveLogger
{
    static PrefabSaveLogger()
    {
        // EditorSceneManager.sceneSaved += OnSceneSaved;
        PrefabStage.prefabSaving += OnPrefabSaving;
        PrefabStage.prefabStageClosing += OnPrefabSaving;
    }

    private static void OnSceneSaved(Scene scene)
    {
        Debug.Log($"[SceneSaveWatcher] Scene saved: {scene.path}");

        // 示例：记录日志到文件
        // string log = $"[{System.DateTime.Now:yyyy-MM-dd HH:mm:ss}] Saved Scene: {scene.path}\n";
        // System.IO.File.AppendAllText("Assets/Editor/scene_save_log.txt", log);
    }

    private static void OnPrefabSaving(PrefabStage prefabStage)
    {
        if (prefabStage.prefabContentsRoot != null)
        {
            Debug.Log("退出Prefab编辑模式，保存Prefab: " + prefabStage.prefabContentsRoot.name);
        }
    }

    private static void OnPrefabSaving(GameObject prefabRoot)
    {
        if (IsFromScene(prefabRoot))
        {
            string prefabPath = PrefabUtility.GetPrefabAssetPathOfNearestInstanceRoot(prefabRoot);
            string sceneName = prefabRoot.scene.name;

            string log =
                $"[Prefab保存] 时间: {System.DateTime.Now:yyyy-MM-dd HH:mm:ss}, 对象: {prefabRoot.name}, 场景: {sceneName}, 路径: {prefabPath}";
            Debug.Log(log);

            // 可选：写入文件
            // File.AppendAllText("Assets/Editor/Logs/prefab_save_log.txt", log + "\n");

            PrefabSaveSettings(prefabRoot);
        }
    }

    private static void PrefabSaveSettings(GameObject prefabRoot)
    {
        // 递归遍历 gameobject以及其子物体
        TraverseChildren(prefabRoot.transform);
    }

    private static void TraverseChildren(Transform current)
    {
        Debug.Log($"Object Name: {current.name}");
        foreach (Transform child in current)
        {
            // 递归调用
            TraverseChildren(child);
        }
    }


    private static bool IsFromScene(GameObject obj)
    {
        var scene = obj.scene;
        return scene.IsValid() && scene.isLoaded && !string.IsNullOrEmpty(scene.name);
    }
}