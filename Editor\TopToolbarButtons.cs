﻿using System;
using System.Reflection;
using UnityEditor;
using UnityEngine;
using UnityEngine.UIElements;

namespace AdapterTool.Editor
{
    /// <summary>
    /// 在 Unity 顶部工具栏中间区域插入按钮
    /// </summary>
    [InitializeOnLoad]
    public static class TopToolbarButtons
    {
        static ScriptableObject toolbar;
        static IMGUIContainer container;

        static TopToolbarButtons()
        {
            EditorApplication.update += TryInject;
        }

        static bool injected; // 用它判断是否已注入

        static void TryInject()
        {
            if (injected) return;

            var tbType = typeof(UnityEditor.Editor).Assembly.GetType("UnityEditor.Toolbar");
            if (tbType == null) return;

            var list = Resources.FindObjectsOfTypeAll(tbType);
            if (list == null || list.Length == 0) return;

            var inst = (ScriptableObject)list[0];

            // 兼容不同版本：属性优先，失败再用私有字段 m_Root
            const BindingFlags flags = BindingFlags.Instance | BindingFlags.Public | BindingFlags.NonPublic;
            var prop = tbType.GetProperty("rootVisualElement", flags);
            var field = tbType.GetField("m_Root", flags);

            var root = (prop?.GetValue(inst) as VisualElement)
                       ?? (field?.GetValue(inst) as VisualElement);

            // 还没建好 UI 树：等下一帧
            if (root == null) return;

            // 找到“中间的 Play/Pause 区域”
            var zone = FindCenterZone(root);
            if (zone == null) return;

            if (container == null)
            {
                container = new IMGUIContainer(DrawGUI)
                {
                    name = "RectOrientationButtons",
                    style = { marginLeft = 6, marginRight = 6 }
                };
            }

            // 放到 Play/Pause 后面（在中间区末尾）
            int insertIndex = zone.childCount;

            // 若能定位到具体的“播放控件”节点，就插在它后面更贴近
            var playNode = zone.Q("PlayControls")
                           ?? zone.Q("ToolbarPlayModeToggle")
                           ?? zone.Q("PlayMode");
            if (playNode != null)
                insertIndex = zone.IndexOf(playNode) + 1;

            // 先移除再插入，避免脚本重载时重复添加
            container.RemoveFromHierarchy();
            zone.Insert(insertIndex, container);

            injected = true;
            EditorApplication.update -= TryInject;
        }

// —— 辅助：尽量命中中间区；找不到就递归找名字里含 “Play” 的节点 ——
        static VisualElement FindCenterZone(VisualElement root)
        {
            string[] candidates =
            {
                "ToolbarZonePlayModes",
                "ToolbarZonePlayMode",
                "ToolbarZonePlayControls",
                "ToolbarZoneCenterAlign",
                "PlayControls"
            };
            foreach (var n in candidates)
            {
                var z = root.Q(n);
                if (z != null) return z;
            }

            VisualElement found = null;

            void DFS(VisualElement ve)
            {
                Debug.Log("enter  dfs: " + ve.name);
                if (found != null) return;
                if (!string.IsNullOrEmpty(ve.name) &&
                    ve.name.IndexOf("play", StringComparison.OrdinalIgnoreCase) >= 0)
                {
                    found = ve;
                    return;
                }

                foreach (var c in ve.Children()) DFS(c);
            }

            DFS(root);
            return found ?? root;
        }


        static void DrawGUI()
        {
            using (new EditorGUILayout.HorizontalScope())
            {
                if (GUILayout.Button(new GUIContent("竖", "Set Portrait"), EditorStyles.miniButtonLeft,
                        GUILayout.Width(24)))
                    SetPortrait();

                if (GUILayout.Button(new GUIContent("横", "Set Landscape"), EditorStyles.miniButtonRight,
                        GUILayout.Width(24)))
                    SetLandscape();
            }
        }
        
        static void SetPortrait()
        {
            GameViewSizeHelper.SetGameViewPortrait();
        }

        static void SetLandscape()
        {
            GameViewSizeHelper.SetGameViewLandscape();
        }
    }
}