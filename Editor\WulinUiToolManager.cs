using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class WulinUiToolManager
{
    public static WulinUiToolManager Instance
    {
        get
        {
            if (_instance == null)
            {
                _instance = new WulinUiToolManager();
            }
            return _instance;
        }
    }

    private static WulinUiToolManager _instance;
    
    public Vector2 PortraitResolution { get; private set; } = new Vector2(750, 1334);
    public Vector2 LandscapeResolution { get; private set; } = new Vector2(1334, 750);

    private WulinUiToolManager()
    {
        
    }

    
}
