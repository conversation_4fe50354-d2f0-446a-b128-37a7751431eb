using UnityEngine;

namespace AdapterTool.Runtime
{
    /// <summary>
    /// 测试脚本，用于验证新的适配器架构
    /// </summary>
    public class AdapterTest : MonoBehaviour
    {
        [Header("测试按钮")]
        [SerializeField] private KeyCode toggleKey = KeyCode.Space;
        
        private void Update()
        {
            if (Input.GetKeyDown(toggleKey))
            {
                TestToggleOrientation();
            }
            
            if (Input.GetKeyDown(KeyCode.P))
            {
                TestSetPortrait();
            }
            
            if (Input.GetKeyDown(KeyCode.L))
            {
                TestSetLandscape();
            }
        }
        
        private void TestToggleOrientation()
        {
            if (AdapterManager.Instance != null)
            {
                Debug.Log("=== 测试切换朝向 ===");
                AdapterManager.Instance.ToggleOrientation();
            }
            else
            {
                Debug.LogWarning("AdapterManager.Instance 为空！");
            }
        }
        
        private void TestSetPortrait()
        {
            if (AdapterManager.Instance != null)
            {
                Debug.Log("=== 测试设置为竖屏 ===");
                AdapterManager.Instance.SetOrientation(ScreenOrientationType.Portrait);
            }
        }
        
        private void TestSetLandscape()
        {
            if (AdapterManager.Instance != null)
            {
                Debug.Log("=== 测试设置为横屏 ===");
                AdapterManager.Instance.SetOrientation(ScreenOrientationType.Landscape);
            }
        }
        
        private void OnGUI()
        {
            GUILayout.BeginArea(new Rect(10, 10, 300, 200));
            GUILayout.Label("适配器测试面板", GUI.skin.box);
            
            if (AdapterManager.Instance != null)
            {
                GUILayout.Label($"当前朝向: {AdapterManager.Instance.GetCurrentOrientation()}");
                GUILayout.Label($"已注册适配器数量: {GetRegisteredAdapterCount()}");
                
                GUILayout.Space(10);
                
                if (GUILayout.Button("切换朝向 (Space)"))
                {
                    TestToggleOrientation();
                }
                
                if (GUILayout.Button("设置为竖屏 (P)"))
                {
                    TestSetPortrait();
                }
                
                if (GUILayout.Button("设置为横屏 (L)"))
                {
                    TestSetLandscape();
                }
            }
            else
            {
                GUILayout.Label("AdapterManager 未找到！", GUI.skin.box);
            }
            
            GUILayout.EndArea();
        }
        
        private int GetRegisteredAdapterCount()
        {
            // 通过反射获取注册的适配器数量（仅用于测试显示）
            if (AdapterManager.Instance != null)
            {
                var field = typeof(AdapterManager).GetField("m_RegisteredAdapters", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                if (field != null)
                {
                    var list = field.GetValue(AdapterManager.Instance) as System.Collections.Generic.List<IAdapter>;
                    return list?.Count ?? 0;
                }
            }
            return 0;
        }
    }
}
